{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ok/chatbot/frontend/src/components/VoiceChatbot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport axios from 'axios';\n\ninterface ChatMessage {\n  id: string;\n  type: 'user' | 'assistant';\n  text: string;\n  audioUrl?: string;\n  timestamp: Date;\n}\n\nconst VoiceChatbot = () => {\n  const [isRecording, setIsRecording] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [isSpacePressed, setIsSpacePressed] = useState(false);\n  const [isDemoMode, setIsDemoMode] = useState(false);\n\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioChunksRef = useRef<Blob[]>([]);\n  const streamRef = useRef<MediaStream | null>(null);\n\n  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n\n  // Space tuşu event listener'ları\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.code === 'Space' && !isSpacePressed && !isProcessing) {\n        event.preventDefault();\n        setIsSpacePressed(true);\n        startRecording();\n      }\n    };\n\n    const handleKeyUp = (event: KeyboardEvent) => {\n      if (event.code === 'Space' && isSpacePressed) {\n        event.preventDefault();\n        setIsSpacePressed(false);\n        stopRecording();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('keyup', handleKeyUp);\n\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('keyup', handleKeyUp);\n    };\n  }, [isSpacePressed, isProcessing]);\n\n  // Mikrofon erişimi ve kayıt başlatma\n  const startRecording = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        } \n      });\n      \n      streamRef.current = stream;\n      audioChunksRef.current = [];\n\n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      });\n      \n      mediaRecorderRef.current = mediaRecorder;\n\n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n\n      mediaRecorder.onstop = () => {\n        processAudio();\n      };\n\n      mediaRecorder.start();\n      setIsRecording(true);\n      \n    } catch (error) {\n      console.error('Mikrofon erişimi hatası:', error);\n      alert('Mikrofon erişimi reddedildi. Lütfen tarayıcı ayarlarından mikrofon iznini kontrol edin.');\n    }\n  };\n\n  // Kayıt durdurma\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop();\n      setIsRecording(false);\n      \n      // Stream'i temizle\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop());\n        streamRef.current = null;\n      }\n    }\n  };\n\n  // Demo modu için simüle edilmiş yanıt\n  const processDemoAudio = async () => {\n    setIsProcessing(true);\n\n    // Kullanıcı mesajını ekle\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      type: 'user',\n      text: 'Demo ses kaydı (gerçek ses tanıma için API anahtarları gerekli)',\n      timestamp: new Date(),\n    };\n    setMessages(prev => [...prev, userMessage]);\n\n    // 2 saniye bekle (gerçek API çağrısını simüle et)\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // Demo yanıtı ekle\n    const assistantMessage: ChatMessage = {\n      id: (Date.now() + 1).toString(),\n      type: 'assistant',\n      text: 'Bu bir demo yanıtıdır. Gerçek AI yanıtları için OpenAI ve ElevenLabs API anahtarlarını backend/.env dosyasına ekleyin.',\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, assistantMessage]);\n    setIsProcessing(false);\n  };\n\n  // Ses dosyasını işleme\n  const processAudio = async () => {\n    if (audioChunksRef.current.length === 0) return;\n\n    // Demo modunda simüle edilmiş işlem\n    if (isDemoMode) {\n      return processDemoAudio();\n    }\n\n    setIsProcessing(true);\n\n    try {\n      // Ses blob'unu oluştur\n      const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });\n\n      // FormData oluştur\n      const formData = new FormData();\n      formData.append('audio', audioBlob, 'recording.webm');\n\n      // Kullanıcı mesajını ekle (geçici)\n      const userMessage: ChatMessage = {\n        id: Date.now().toString(),\n        type: 'user',\n        text: 'Ses kaydı işleniyor...',\n        timestamp: new Date(),\n      };\n      setMessages(prev => [...prev, userMessage]);\n\n      // Backend'e gönder\n      const response = await axios.post(`${API_BASE_URL}/audio/process`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n        timeout: 30000, // 30 saniye timeout\n      });\n\n      if (response.data.success) {\n        // Kullanıcı mesajını güncelle\n        const updatedUserMessage: ChatMessage = {\n          ...userMessage,\n          text: response.data.transcription,\n        };\n\n        // Asistan yanıtını ekle\n        const assistantMessage: ChatMessage = {\n          id: (Date.now() + 1).toString(),\n          type: 'assistant',\n          text: response.data.response_text,\n          audioUrl: response.data.audio_url,\n          timestamp: new Date(),\n        };\n\n        setMessages(prev => [\n          ...prev.slice(0, -1), // Son mesajı çıkar\n          updatedUserMessage,\n          assistantMessage\n        ]);\n\n        // Yanıt sesini çal\n        if (response.data.audio_url) {\n          const audio = new Audio(response.data.audio_url);\n          audio.play().catch(console.error);\n        }\n\n      } else {\n        throw new Error(response.data.error || 'Ses işleme hatası');\n      }\n\n    } catch (error: any) {\n      console.error('Ses işleme hatası:', error);\n\n      let errorText = 'Üzgünüm, ses işlenirken bir hata oluştu.';\n\n      if (error.response?.status === 500) {\n        errorText = 'Sunucu hatası. API anahtarlarının doğru yapılandırıldığından emin olun.';\n      } else if (error.code === 'ECONNREFUSED') {\n        errorText = 'Backend sunucusuna bağlanılamıyor. Sunucunun çalıştığından emin olun.';\n      } else if (error.response?.data?.error) {\n        errorText = error.response.data.error;\n      }\n\n      // Hata mesajı ekle\n      const errorMessage: ChatMessage = {\n        id: Date.now().toString(),\n        type: 'assistant',\n        text: errorText,\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev.slice(0, -1), errorMessage]);\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      {/* Chat Mesajları */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6 h-96 overflow-y-auto\">\n        {messages.length === 0 ? (\n          <div className=\"text-center text-gray-500 dark:text-gray-400 mt-20\">\n            <p className=\"text-lg mb-2\">Konuşmaya başlamak için Space tuşuna basılı tutun</p>\n            <p className=\"text-sm\">🎤 Mikrofon hazır</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                    message.type === 'user'\n                      ? 'bg-blue-500 text-white'\n                      : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.text}</p>\n                  {message.audioUrl && (\n                    <audio controls className=\"mt-2 w-full\">\n                      <source src={message.audioUrl} type=\"audio/mpeg\" />\n                    </audio>\n                  )}\n                  <p className=\"text-xs opacity-70 mt-1\">\n                    {message.timestamp.toLocaleTimeString()}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Kontrol Paneli */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        {/* Demo Mode Toggle */}\n        <div className=\"flex justify-center mb-4\">\n          <label className=\"flex items-center space-x-2 cursor-pointer\">\n            <input\n              type=\"checkbox\"\n              checked={isDemoMode}\n              onChange={(e) => setIsDemoMode(e.target.checked)}\n              className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n            />\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Demo Modu (API anahtarları olmadan test)\n            </span>\n          </label>\n        </div>\n\n        <div className=\"text-center\">\n          {/* Kayıt Butonu */}\n          <div className=\"mb-4\">\n            <button\n              className={`w-20 h-20 rounded-full flex items-center justify-center text-white text-2xl transition-all duration-200 ${\n                isRecording\n                  ? 'bg-red-500 animate-pulse scale-110'\n                  : isProcessing\n                  ? 'bg-yellow-500 animate-spin'\n                  : 'bg-blue-500 hover:bg-blue-600'\n              }`}\n              disabled={isProcessing}\n            >\n              {isProcessing ? '⏳' : isRecording ? '🔴' : '🎤'}\n            </button>\n          </div>\n\n          {/* Durum Metni */}\n          <div className=\"text-center\">\n            {isRecording && (\n              <p className=\"text-red-500 font-medium animate-pulse\">\n                🔴 Kayıt yapılıyor... Space tuşunu bırakın\n              </p>\n            )}\n            {isProcessing && (\n              <p className=\"text-yellow-500 font-medium\">\n                ⏳ Ses işleniyor...\n              </p>\n            )}\n            {!isRecording && !isProcessing && (\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                Space tuşuna basılı tutarak konuşun\n              </p>\n            )}\n          </div>\n\n          {/* Yardım Metni */}\n          <div className=\"mt-4 text-sm text-gray-500 dark:text-gray-400\">\n            <p>💡 İpucu: Space tuşuna basılı tutun, konuşun ve bırakın</p>\n            {isDemoMode && (\n              <p className=\"mt-2 text-yellow-600 dark:text-yellow-400\">\n                🧪 Demo modu aktif - Gerçek API çağrıları yapılmayacak\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VoiceChatbot;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,eAAe;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IACxC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,MAAM,eAAe,iEAAmC;IAExD,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,MAAM,IAAI,KAAK,WAAW,CAAC,kBAAkB,CAAC,cAAc;gBAC9D,MAAM,cAAc;gBACpB,kBAAkB;gBAClB;YACF;QACF;QAEA,MAAM,cAAc,CAAC;YACnB,IAAI,MAAM,IAAI,KAAK,WAAW,gBAAgB;gBAC5C,MAAM,cAAc;gBACpB,kBAAkB;gBAClB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,gBAAgB,CAAC,SAAS;QAEjC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;YACtC,OAAO,mBAAmB,CAAC,SAAS;QACtC;IACF,GAAG;QAAC;QAAgB;KAAa;IAEjC,qCAAqC;IACrC,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;oBACL,kBAAkB;oBAClB,kBAAkB;oBAClB,iBAAiB;gBACnB;YACF;YAEA,UAAU,OAAO,GAAG;YACpB,eAAe,OAAO,GAAG,EAAE;YAE3B,MAAM,gBAAgB,IAAI,cAAc,QAAQ;gBAC9C,UAAU;YACZ;YAEA,iBAAiB,OAAO,GAAG;YAE3B,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,eAAe,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBACxC;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB;YACF;YAEA,cAAc,KAAK;YACnB,eAAe;QAEjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;YAEf,mBAAmB;YACnB,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;gBACzD,UAAU,OAAO,GAAG;YACtB;QACF;IACF;IAEA,sCAAsC;IACtC,MAAM,mBAAmB;QACvB,gBAAgB;QAEhB,0BAA0B;QAC1B,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,MAAM;YACN,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAE1C,kDAAkD;QAClD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,mBAAmB;QACnB,MAAM,mBAAgC;YACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;YAC7B,MAAM;YACN,MAAM;YACN,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAiB;QAC/C,gBAAgB;IAClB;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,IAAI,eAAe,OAAO,CAAC,MAAM,KAAK,GAAG;QAEzC,oCAAoC;QACpC,IAAI,YAAY;YACd,OAAO;QACT;QAEA,gBAAgB;QAEhB,IAAI;YACF,uBAAuB;YACvB,MAAM,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;gBAAE,MAAM;YAAa;YAExE,mBAAmB;YACnB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,WAAW;YAEpC,mCAAmC;YACnC,MAAM,cAA2B;gBAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,MAAM;gBACN,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,mBAAmB;YACnB,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,cAAc,CAAC,EAAE,UAAU;gBAC3E,SAAS;oBACP,gBAAgB;gBAClB;gBACA,SAAS;YACX;YAEA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,8BAA8B;gBAC9B,MAAM,qBAAkC;oBACtC,GAAG,WAAW;oBACd,MAAM,SAAS,IAAI,CAAC,aAAa;gBACnC;gBAEA,wBAAwB;gBACxB,MAAM,mBAAgC;oBACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,MAAM,SAAS,IAAI,CAAC,aAAa;oBACjC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,WAAW,IAAI;gBACjB;gBAEA,YAAY,CAAA,OAAQ;2BACf,KAAK,KAAK,CAAC,GAAG,CAAC;wBAClB;wBACA;qBACD;gBAED,mBAAmB;gBACnB,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;oBAC3B,MAAM,QAAQ,IAAI,MAAM,SAAS,IAAI,CAAC,SAAS;oBAC/C,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;gBAClC;YAEF,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,KAAK,IAAI;YACzC;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YAEpC,IAAI,YAAY;YAEhB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,YAAY;YACd,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,YAAY;YACd,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,OAAO;gBACtC,YAAY,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK;YACvC;YAEA,mBAAmB;YACnB,MAAM,eAA4B;gBAChC,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,MAAM;gBACN,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI,KAAK,KAAK,CAAC,GAAG,CAAC;oBAAI;iBAAa;QAC1D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,8OAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;yCAGzB,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAE9E,cAAA,8OAAC;gCACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,2BACA,8DACJ;;kDAEF,8OAAC;wCAAE,WAAU;kDAAW,QAAQ,IAAI;;;;;;oCACnC,QAAQ,QAAQ,kBACf,8OAAC;wCAAM,QAAQ;wCAAC,WAAU;kDACxB,cAAA,8OAAC;4CAAO,KAAK,QAAQ,QAAQ;4CAAE,MAAK;;;;;;;;;;;kDAGxC,8OAAC;wCAAE,WAAU;kDACV,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;2BAjBpC,QAAQ,EAAE;;;;;;;;;;;;;;;0BA2BzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;oCAC/C,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;;;;;;;;;;;;kCAM/D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,wGAAwG,EAClH,cACI,uCACA,eACA,+BACA,iCACJ;oCACF,UAAU;8CAET,eAAe,MAAM,cAAc,OAAO;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;oCACZ,6BACC,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;oCAIvD,8BACC,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;oCAI5C,CAAC,eAAe,CAAC,8BAChB,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAOpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;oCACF,4BACC,8OAAC;wCAAE,WAAU;kDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE;uCAEe", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/ok/chatbot/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport VoiceChatbot from '@/components/VoiceChatbot';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 dark:text-white mb-2\">\n            Sesli AI Asistan\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300\">\n            Space tuşuna basılı tutarak konuşun, AI asistanınızla sesli etkile<PERSON>im kurun\n          </p>\n        </div>\n\n        <VoiceChatbot />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAKlD,8OAAC,kIAAA,CAAA,UAAY;;;;;;;;;;;;;;;;AAIrB", "debugId": null}}]}