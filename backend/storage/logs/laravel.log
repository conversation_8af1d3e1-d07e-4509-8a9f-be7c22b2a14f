[2025-06-05 18:35:01] local.ERROR: OpenAI Assistant API request failed {"error":"Client error: `POST https://api.openai.com/v1/threads` resulted in a `401 Unauthorized` response:
{
  \"error\": {
    \"message\": \"Incorrect API key provided: your_ope************here. You can find your API key at https: (truncated...)
","response":"{
  \"error\": {
    \"message\": \"Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.\",
    \"type\": \"invalid_request_error\",
    \"param\": null,
    \"code\": \"invalid_api_key\"
  }
}"} 
[2025-06-05 18:35:01] local.ERROR: OpenAI service error {"error":"ChatGPT Assistant request failed"} 
[2025-06-05 18:35:01] local.ERROR: Chat message failed {"error":"ChatGPT Assistant request failed","message":"<PERSON><PERSON><PERSON><PERSON>, nas<PERSON><PERSON><PERSON>n?"} 
